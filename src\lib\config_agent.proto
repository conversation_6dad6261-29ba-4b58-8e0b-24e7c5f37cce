// Protocol buffer modified on 2020-07-22 17:42:56
// Protocol buffer modified on 2022-12-30 18:24:59
syntax = "proto2";
package config_req;
message Agent {
  optional uint32 id       = 1;
  optional string name     = 2;
  optional string ip       = 3;
  optional string creator  = 4;
  optional string status   = 5;
  optional string comment  = 6;
  optional string disabled = 7;
  optional string serial   = 8;
}
message Device {
  optional uint32 id         = 1;
  optional string name       = 2;
  optional string type       = 3;
  optional string model      = 4;
  optional uint32 agentid    = 5;
  optional string creator    = 6;
  optional string comment    = 7;
  optional string ip         = 8;
  optional uint32 port       = 9;
  optional string disabled   = 10;
  optional string flowtype   = 11;
  optional string interface  = 12;
  optional uint32 pcap_level = 13;
  optional string temp       = 14;
  optional string filter     = 15;
}
message Controller {
  optional uint32 id    = 1;
  optional string name  = 2;
  optional string value = 3;
}
