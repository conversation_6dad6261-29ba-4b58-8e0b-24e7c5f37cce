openapi: 3.0.3
info:
  title: 流量分析系统 API
  description: |
    基于C++开发的网络流量分析系统，提供网络安全监控、流量分析、威胁检测等功能。

    ## 系统架构
    - **服务器端**: C++ CGI程序，部署在 `/Server/www/d/` 目录
    - **数据库**: MySQL数据库
    - **协议**: Protocol Buffers数据序列化
    - **认证**: 基于Session的用户认证机制

  version: 1.0.0
  contact:
    name: API Support
  license:
    name: MIT
servers:
  - url: http://localhost/d
    description: 开发环境
  - url: https://api.example.com/d
    description: 生产环境

security:
  - SessionAuth: []

paths:
  /auth:
    post:
      tags:
        - 认证管理
      summary: 用户认证
      description: 用户登录认证和权限验证
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                auth_target:
                  type: string
                  description: 目标API名称
                  enum: [mo, feature, topn, login, logout, event, config, bwlist, internalip, ipinfo, portinfo, locinfo, threatinfo, threatinfopro, geoinfo, auth_status, sctl, event_feature, evidence]
                username:
                  type: string
                  description: 用户名
                password:
                  type: string
                  description: 密码
              required:
                - auth_target
      responses:
        '200':
          description: 认证成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '403':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /login:
    post:
      tags:
        - 认证管理
      summary: 用户登录
      description: 用户登录获取session
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                username:
                  type: string
                  description: 用户名
                password:
                  type: string
                  description: 密码
              required:
                - username
                - password
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: 登录失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /logout:
    get:
      tags:
        - 认证管理
      summary: 用户登出
      description: 用户登出，清除session
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /auth_status:
    get:
      tags:
        - 认证管理
      summary: 认证状态查询
      description: 查询当前用户认证状态
      responses:
        '200':
          description: 状态查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthStatusResponse'

  /feature:
    get:
      tags:
        - 流量分析
      summary: 流量特征提取
      description: 提取网络流量特征数据
      parameters:
        - name: devid
          in: query
          required: true
          schema:
            type: integer
          description: 设备ID
        - name: starttime
          in: query
          schema:
            type: integer
          description: 开始时间戳
        - name: endtime
          in: query
          schema:
            type: integer
          description: 结束时间戳
        - name: ip
          in: query
          schema:
            type: string
          description: IP地址
        - name: port
          in: query
          schema:
            type: integer
          description: 端口号
        - name: type
          in: query
          schema:
            type: string
            enum: [SUS, POP, PORT_SCAN, SERVICE, TCPINIT, FORCE, DNS_TUN, FLOOD, BLACK, WHITE, ASSET_IP, MO, DNS, ASSET_URL, ASSET_HOST, ASSET_SRV, URL_CONTENT, DGA, IP_SCAN, API]
          description: 特征类型
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
          description: 返回记录数限制
        - name: dbg
          in: query
          schema:
            type: integer
            enum: [0, 1]
          description: 调试模式
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FeatureRecord'
        '400':
          description: 参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /event_feature:
    get:
      tags:
        - 流量分析
      summary: 事件特征提取
      description: 提取安全事件特征数据
      parameters:
        - name: devid
          in: query
          required: true
          schema:
            type: integer
          description: 设备ID
        - name: starttime
          in: query
          schema:
            type: integer
          description: 开始时间戳
        - name: endtime
          in: query
          schema:
            type: integer
          description: 结束时间戳
        - name: ip
          in: query
          schema:
            type: string
          description: IP地址
        - name: port
          in: query
          schema:
            type: integer
          description: 端口号
        - name: type
          in: query
          schema:
            type: string
            enum: [TI, IP_SCAN, PORT_SCAN, SRV, DNS_TUN, BLACK, MO, DNS, DGA, ICMP_TUN, FRN_TRIP, CAP, URL_CONTENT, DNSTUN_AI, MINING]
          description: 事件类型
        - name: domain
          in: query
          schema:
            type: string
          description: 域名
        - name: url
          in: query
          schema:
            type: string
          description: URL地址
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
          description: 返回记录数限制
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EventFeatureRecord'

  /mo:
    get:
      tags:
        - 监控管理
      summary: 查询监控对象
      description: 查询网络监控对象信息
      parameters:
        - name: op
          in: query
          required: true
          schema:
            type: string
            enum: [GET, GET_FILTER]
          description: 操作类型
        - name: devid
          in: query
          schema:
            type: integer
          description: 设备ID
        - name: moid
          in: query
          schema:
            type: string
          description: 监控对象ID列表
        - name: mogid
          in: query
          schema:
            type: integer
          description: 监控对象组ID
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MonitorObject'
    post:
      tags:
        - 监控管理
      summary: 管理监控对象
      description: 添加、修改、删除监控对象
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                op:
                  type: string
                  enum: [ADD, DEL, MOD, GADD, GDEL, GMOD]
                  description: 操作类型
                moip:
                  type: string
                  description: 监控IP地址
                moport:
                  type: string
                  description: 监控端口
                protocol:
                  type: string
                  description: 协议类型
                pip:
                  type: string
                  description: 对端IP地址
                pport:
                  type: string
                  description: 对端端口
                desc:
                  type: string
                  description: 描述信息
                tag:
                  type: string
                  description: 标签
                moid:
                  type: string
                  description: 监控对象ID
                mogid:
                  type: integer
                  description: 监控对象组ID
                devid:
                  type: integer
                  description: 设备ID
                direction:
                  type: string
                  description: 方向
                filter:
                  type: string
                  description: 过滤条件
              required:
                - op
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /event:
    get:
      tags:
        - 事件管理
      summary: 安全事件查询
      description: 查询安全事件数据
      parameters:
        - name: starttime
          in: query
          schema:
            type: integer
          description: 开始时间戳
        - name: endtime
          in: query
          schema:
            type: integer
          description: 结束时间戳
        - name: step
          in: query
          schema:
            type: integer
          description: 时间步长
        - name: type
          in: query
          schema:
            type: string
          description: 事件类型
        - name: devid
          in: query
          schema:
            type: integer
          description: 设备ID
        - name: event_id
          in: query
          schema:
            type: integer
          description: 事件ID
        - name: id
          in: query
          schema:
            type: integer
          description: 记录ID
        - name: obj
          in: query
          schema:
            type: string
          description: 事件对象
        - name: level
          in: query
          schema:
            type: string
          description: 事件级别
        - name: req_type
          in: query
          schema:
            type: string
            enum: [ORI, AGGRE, SET_PROC_STATUS]
          description: 请求类型
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EventRecord'

  /sctl:
    get:
      tags:
        - 系统控制
      summary: 系统控制
      description: 系统服务控制和状态查询
      parameters:
        - name: nodetype
          in: query
          required: true
          schema:
            type: string
            enum: [ALL, SERVER, AGENT, PROBE]
          description: 节点类型
        - name: servicetype
          in: query
          required: true
          schema:
            type: string
            enum: [BASIC, SSH, HTTP, DISK]
          description: 服务类型
        - name: op
          in: query
          required: true
          schema:
            type: string
            enum: [STATUS, START, STOP, RESTART]
          description: 操作类型
        - name: id
          in: query
          schema:
            type: string
          description: 节点ID
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SystemControlRecord'

  /config:
    get:
      tags:
        - 配置管理
      summary: 查询系统配置
      description: 查询系统配置信息
      parameters:
        - name: type
          in: query
          required: true
          schema:
            type: string
            enum: [event, mo, internalip, internalsrv, agent, bwlist, user]
          description: 配置类型
        - name: op
          in: query
          required: true
          schema:
            type: string
            enum: [get]
          description: 操作类型
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigResponse'
    post:
      tags:
        - 配置管理
      summary: 管理系统配置
      description: 添加、修改、删除系统配置
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                type:
                  type: string
                  enum: [event, mo, internalip, internalsrv, agent, bwlist, user]
                  description: 配置类型
                op:
                  type: string
                  enum: [add, mod, del]
                  description: 操作类型
              required:
                - type
                - op
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /bwlist:
    get:
      tags:
        - 配置管理
      summary: 黑白名单管理
      description: 查询黑白名单配置
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BWListResponse'

  /locinfo:
    get:
      tags:
        - 地理位置
      summary: IP位置信息
      description: 查询IP地址的地理位置信息
      parameters:
        - name: iplist
          in: query
          required: true
          schema:
            type: string
          description: IP地址列表，逗号分隔
          example: "*******,*******"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LocationInfo'

  /geoinfo:
    get:
      tags:
        - 地理位置
      summary: 地理信息查询
      description: 查询IP地址的详细地理信息（支持中英文）
      parameters:
        - name: iplist
          in: query
          required: true
          schema:
            type: string
          description: IP地址列表，逗号分隔
          example: "*******,*******"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GeoInfo'

  /threatinfo:
    get:
      tags:
        - 威胁情报
      summary: 威胁情报查询
      description: 查询威胁情报信息
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThreatInfo'
        '403':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /threatinfopro:
    get:
      tags:
        - 威胁情报
      summary: 威胁情报专业版
      description: 专业版威胁情报查询
      security:
        - TokenAuth: []
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThreatInfoPro'

  /ipinfo:
    get:
      tags:
        - 网络信息
      summary: IP信息查询
      description: 查询IP相关信息
      parameters:
        - name: iplist
          in: query
          required: true
          schema:
            type: string
          description: IP地址列表
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/IPInfo'

  /portinfo:
    get:
      tags:
        - 网络信息
      summary: 端口信息查询
      description: 查询端口相关信息
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PortInfo'

  /internalip:
    get:
      tags:
        - 内网管理
      summary: 查询内网IP
      description: 查询内网IP地址
      parameters:
        - name: op
          in: query
          required: true
          schema:
            type: string
            enum: [GET]
          description: 操作类型
        - name: devid
          in: query
          schema:
            type: integer
          description: 设备ID
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InternalIP'
    post:
      tags:
        - 内网管理
      summary: 管理内网IP
      description: 添加、修改、删除内网IP地址
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                op:
                  type: string
                  enum: [ADD, DEL, MOD]
                  description: 操作类型
                id:
                  type: string
                  description: 记录ID
                ip:
                  type: string
                  description: IP地址
                devid:
                  type: integer
                  description: 设备ID
                desc:
                  type: string
                  description: 描述信息
              required:
                - op
      responses:
        '200':
          description: 操作成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /evidence:
    get:
      tags:
        - 证据提取
      summary: 数据包证据提取
      description: 提取网络数据包证据
      parameters:
        - name: devid
          in: query
          schema:
            type: integer
          description: 设备ID
        - name: time
          in: query
          schema:
            type: integer
          description: 时间戳（微秒）
        - name: time_sec
          in: query
          schema:
            type: integer
          description: 时间戳（秒）
        - name: time_usec
          in: query
          schema:
            type: integer
          description: 时间戳（微秒部分）
        - name: ip
          in: query
          schema:
            type: string
          description: IP地址
        - name: port
          in: query
          schema:
            type: integer
          description: 端口号
        - name: download
          in: query
          schema:
            type: boolean
          description: 是否下载
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EvidenceRecord'
            application/octet-stream:
              schema:
                type: string
                format: binary

  /topn:
    get:
      tags:
        - 统计分析
      summary: TopN统计查询
      description: 获取流量TopN统计数据
      parameters:
        - name: devid
          in: query
          required: true
          schema:
            type: integer
          description: 设备ID
        - name: starttime
          in: query
          schema:
            type: integer
          description: 开始时间戳
        - name: endtime
          in: query
          schema:
            type: integer
          description: 结束时间戳
        - name: sortby
          in: query
          schema:
            type: string
          description: 排序字段
        - name: orderby
          in: query
          schema:
            type: string
            enum: [ASC, DESC]
          description: 排序方式
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
          description: 返回记录数
        - name: step
          in: query
          schema:
            type: integer
          description: 时间步长
        - name: ip
          in: query
          schema:
            type: string
          description: IP地址
        - name: port
          in: query
          schema:
            type: integer
          description: 端口号
        - name: proto
          in: query
          schema:
            type: integer
          description: 协议
        - name: include
          in: query
          schema:
            type: string
          description: 包含条件
        - name: exclude
          in: query
          schema:
            type: string
          description: 排除条件
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TopNRecord'

components:
  securitySchemes:
    SessionAuth:
      type: apiKey
      in: cookie
      name: SESSION_ID
    ApiKeyAuth:
      type: apiKey
      in: query
      name: key
    TokenAuth:
      type: apiKey
      in: query
      name: token

  schemas:
    # 通用响应模式
    SuccessResponse:
      type: object
      properties:
        result:
          type: string
          enum: [success]
        data:
          type: object

    ErrorResponse:
      type: object
      properties:
        result:
          type: string
          enum: [failed]
        desc:
          type: string
          description: 错误描述

    # 认证相关
    AuthResponse:
      type: object
      properties:
        code:
          type: integer
          description: 认证状态码
        user_info:
          type: object
          properties:
            username:
              type: string
            role:
              type: string
              enum: [SYSADMIN, ANALYSER, VIEWER]

    LoginResponse:
      type: object
      properties:
        result:
          type: string
          enum: [success]
        session_id:
          type: string
        user_info:
          type: object

    AuthStatusResponse:
      type: object
      properties:
        authenticated:
          type: boolean
        username:
          type: string
        role:
          type: string

    # 流量特征记录
    FeatureRecord:
      type: object
      properties:
        devid:
          type: integer
          description: 设备ID
        type:
          type: string
          description: 特征类型
        time:
          type: integer
          description: 时间戳
        duration:
          type: integer
          description: 持续时间
        protocol:
          type: integer
          description: 协议号
        bytes:
          type: integer
          format: int64
          description: 字节数
        flows:
          type: integer
          format: int64
          description: 流数量
        pkts:
          type: integer
          format: int64
          description: 包数量
        sip:
          type: string
          description: 源IP地址
        sport:
          type: integer
          description: 源端口
        dip:
          type: string
          description: 目标IP地址
        dport:
          type: integer
          description: 目标端口
        peers:
          type: integer
          format: int64
          description: 对端数量
        ip:
          type: string
          description: IP地址
        port:
          type: integer
          description: 端口号
        peak_bytes:
          type: integer
          format: int64
          description: 峰值字节数
        peak_pkts:
          type: integer
          format: int64
          description: 峰值包数
        peak_flows:
          type: integer
          format: int64
          description: 峰值流数
        moid:
          type: integer
          description: 监控对象ID
        bwclass:
          type: string
          description: 带宽类别
        ti_mark:
          type: string
          description: 威胁情报标记
        srv_mark:
          type: string
          description: 服务标记
        qname:
          type: string
          description: 查询域名
        qtype:
          type: integer
          description: 查询类型
        url:
          type: string
          description: URL地址

    # 事件特征记录
    EventFeatureRecord:
      type: object
      properties:
        devid:
          type: integer
          description: 设备ID
        type:
          type: integer
          description: 事件类型
        time:
          type: integer
          description: 时间戳
        protocol:
          type: integer
          description: 协议号
        bytes:
          type: integer
          format: int64
          description: 字节数
        flows:
          type: integer
          format: int64
          description: 流数量
        pkts:
          type: integer
          format: int64
          description: 包数量
        sip:
          type: string
          description: 源IP地址
        sport:
          type: integer
          description: 源端口
        dip:
          type: string
          description: 目标IP地址
        dport:
          type: integer
          description: 目标端口
        domain:
          type: string
          description: 域名
        qtype:
          type: integer
          description: 查询类型
        url:
          type: string
          description: URL地址
        retcode:
          type: integer
          description: 返回码
        obj:
          type: string
          description: 事件对象
        model:
          type: integer
          description: 模型ID
        payload:
          type: string
          description: 载荷数据
        captype:
          type: string
          description: 捕获类型
        capname:
          type: string
          description: 捕获名称
        capvers:
          type: string
          description: 捕获版本
        capusec:
          type: integer
          format: int64
          description: 捕获微秒时间戳
        icmp_type:
          type: integer
          description: ICMP类型

## API使用示例

### 1. 用户登录示例
```bash
curl -X POST "http://server/d/login" \
  -d "username=admin&password=123456"
```

### 2. 查询流量特征示例
```bash
curl "http://server/d/feature?devid=1&starttime=1640995200&endtime=1641081600&type=SUS&limit=20"
```

### 3. 获取系统状态示例
```bash
curl "http://server/d/sctl?nodetype=ALL&servicetype=BASIC&op=STATUS"
```

### 4. 查询IP地理位置示例
```bash
curl "http://server/d/locinfo?iplist=*******,*******"
```

### 5. 监控对象管理示例
```bash
# 添加监控对象
curl -X POST "http://server/d/mo" \
  -d "op=ADD&moip=*************&moport=80&protocol=TCP&desc=Web服务器"

# 查询监控对象
curl "http://server/d/mo?op=GET&devid=1"
```

## 错误代码说明

### HTTP状态码
- `200`: 请求成功
- `400`: 参数错误 (Invalid Params)
- `403`: 认证失败或权限不足
- `500`: 服务器内部错误

### 业务错误码
- `result: "failed"`: 操作失败
- `result: "success"`: 操作成功

## 数据结构说明

### 流量特征记录 (FeatureRecord)
```json
{
  "devid": 1,
  "type": "SUS",
  "time": 1640995200,
  "duration": 300,
  "protocol": 6,
  "bytes": 1024000,
  "flows": 100,
  "pkts": 2000,
  "sip": "*************",
  "sport": 80,
  "dip": "********",
  "dport": 8080,
  "peers": 50
}
```

### 事件记录 (EventRecord)
```json
{
  "devid": 1,
  "type": 2,
  "time": 1640995200,
  "protocol": 6,
  "sip": "*************",
  "sport": 80,
  "dip": "********",
  "dport": 8080,
  "domain": "example.com",
  "url": "/api/data"
}
```

### 监控对象记录 (MoRecord)
```json
{
  "id": 1,
  "moip": "*************",
  "moport": "80",
  "protocol": "TCP",
  "pip": "********",
  "pport": "8080",
  "desc": "Web服务器",
  "tag": "production",
  "devid": 1
}
```

## 性能优化建议

### 1. 查询优化
- 合理设置时间范围，避免查询过长时间段的数据
- 使用limit参数限制返回记录数
- 对于大数据量查询，建议使用分页机制

### 2. 缓存策略
- 系统支持数据缓存，重复查询相同参数会有性能提升
- 建议客户端实现适当的缓存机制

### 3. 并发控制
- 系统支持多并发请求
- 建议控制并发数量，避免对系统造成过大压力

## 安全注意事项

### 1. 认证安全
- 所有敏感操作都需要通过认证
- Session有效期限制，需要定期重新登录
- 支持多级权限控制

### 2. 参数验证
- 系统对所有输入参数进行严格验证
- 防止SQL注入和XSS攻击
- IP地址和端口号格式验证

### 3. 数据安全
- 敏感数据传输建议使用HTTPS
- 数据库连接使用加密通道
- 日志记录不包含敏感信息

## 监控和日志

### 日志文件位置
- 通用日志: `/Server/log/common`
- 命令日志: `/Server/log/cmd`
- Web访问日志: `/Server/log/www`

### 监控指标
- API响应时间
- 请求成功率
- 系统资源使用情况
- 数据库连接状态

## 故障排除

### 常见问题

#### 1. 认证失败
- 检查用户名密码是否正确
- 确认用户权限是否足够
- 检查Session是否过期

#### 2. 参数错误
- 确认必需参数是否提供
- 检查参数格式是否正确
- 验证时间戳格式

#### 3. 数据库连接问题
- 检查数据库服务状态
- 确认配置文件设置正确
- 验证数据库用户权限

#### 4. 系统服务异常
- 检查相关系统服务状态
- 查看系统日志文件
- 确认网络连接正常

### 调试方法
1. 使用`dbg=1`参数启用调试模式
2. 查看相应的日志文件
3. 使用系统控制接口检查服务状态
4. 验证数据库连接和数据完整性

## 版本更新记录

### v1.0 (2025-08-07)
- 初始版本发布
- 包含所有核心API接口
- 支持基本的流量分析和安全监控功能

---
文档版本：1.0
最后更新：2025-08-07
