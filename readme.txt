# 流量分析系统 API 接口文档

## 概述
本系统是一个基于C++开发的网络流量分析系统，使用CGI架构提供Web API接口。系统主要用于网络安全监控、流量分析、威胁检测等功能。

## 系统架构
- **服务器端**: C++ CGI程序，部署在 `/Server/www/d/` 目录
- **数据库**: MySQL数据库，配置文件 `/etc/my.cnf.d/gl.server.cnf`
- **协议**: 使用Protocol Buffers进行数据序列化
- **认证**: 基于Session的用户认证机制

## API接口列表

### 1. 认证相关接口

#### 1.1 auth - 用户认证
**路径**: `/d/auth`
**方法**: GET/POST
**功能**: 用户登录认证和权限验证
**参数**:
- `auth_target`: 目标API名称
- `username`: 用户名
- `password`: 密码
**响应**: JSON格式，包含认证状态和用户信息

#### 1.2 login - 用户登录
**路径**: `/d/login`
**方法**: POST
**功能**: 用户登录
**参数**:
- `username`: 用户名
- `password`: 密码
**响应**: 登录成功返回session信息

#### 1.3 logout - 用户登出
**路径**: `/d/logout`
**方法**: GET/POST
**功能**: 用户登出，清除session

#### 1.4 auth_status - 认证状态查询
**路径**: `/d/auth_status`
**方法**: GET
**功能**: 查询当前用户认证状态

### 2. 流量特征分析接口

#### 2.1 feature - 流量特征提取
**路径**: `/d/feature`
**方法**: GET/POST
**功能**: 提取网络流量特征数据
**必需参数**:
- `devid`: 设备ID
**可选参数**:
- `starttime`: 开始时间戳
- `endtime`: 结束时间戳
- `ip`: IP地址
- `port`: 端口号
- `type`: 特征类型 (SUS, POP, PORT_SCAN, SERVICE, TCPINIT, FORCE, DNS_TUN, FLOOD, BLACK, WHITE, ASSET_IP, MO, DNS, ASSET_URL, ASSET_HOST, ASSET_SRV, URL_CONTENT, DGA, IP_SCAN, API)
- `limit`: 返回记录数限制 (默认10)
- `dbg`: 调试模式 (1启用)
**响应**: JSON数组，包含流量特征记录

#### 2.2 event_feature - 事件特征提取
**路径**: `/d/event_feature`
**方法**: GET/POST
**功能**: 提取安全事件特征数据
**必需参数**:
- `devid`: 设备ID
**可选参数**:
- `starttime`: 开始时间戳
- `endtime`: 结束时间戳
- `ip`: IP地址
- `port`: 端口号
- `type`: 事件类型 (TI, IP_SCAN, PORT_SCAN, SRV, DNS_TUN, BLACK, MO, DNS, DGA, ICMP_TUN, FRN_TRIP, CAP, URL_CONTENT, DNSTUN_AI, MINING)
- `domain`: 域名
- `url`: URL地址
- `limit`: 返回记录数限制
**响应**: JSON数组，包含事件特征记录

### 3. 监控对象管理接口

#### 3.1 mo - 监控对象管理
**路径**: `/d/mo`
**方法**: GET/POST
**功能**: 管理网络监控对象
**参数**:
- `op`: 操作类型 (ADD, DEL, MOD, GET, GADD, GDEL, GGET, GET_FILTER, GMOD)
- `moip`: 监控IP地址
- `moport`: 监控端口
- `protocol`: 协议类型
- `pip`: 对端IP地址
- `pport`: 对端端口
- `desc`: 描述信息
- `tag`: 标签
- `moid`: 监控对象ID
- `mogid`: 监控对象组ID
- `devid`: 设备ID
- `direction`: 方向
- `filter`: 过滤条件
**响应**: JSON格式，包含操作结果和监控对象信息

### 4. 事件管理接口

#### 4.1 event - 安全事件查询
**路径**: `/d/event`
**方法**: GET/POST
**功能**: 查询安全事件数据
**参数**:
- `starttime`: 开始时间戳
- `endtime`: 结束时间戳
- `step`: 时间步长
- `type`: 事件类型
- `devid`: 设备ID
- `event_id`: 事件ID
- `id`: 记录ID
- `obj`: 事件对象
- `level`: 事件级别
- `req_type`: 请求类型 (ORI, AGGRE, SET_PROC_STATUS)
**响应**: JSON数组，包含事件记录

### 5. 系统控制接口

#### 5.1 sctl - 系统控制
**路径**: `/d/sctl`
**方法**: GET/POST
**功能**: 系统服务控制和状态查询
**参数**:
- `nodetype`: 节点类型 (ALL, SERVER, AGENT, PROBE)
- `servicetype`: 服务类型 (BASIC, SSH, HTTP, DISK)
- `op`: 操作类型 (STATUS, START, STOP, RESTART)
- `id`: 节点ID
**响应**: JSON数组，包含系统状态信息

### 6. 配置管理接口

#### 6.1 config - 系统配置
**路径**: `/d/config`
**方法**: GET/POST
**功能**: 系统配置管理
**参数**:
- `type`: 配置类型 (event, mo, internalip, internalsrv, agent, bwlist, user)
- `op`: 操作类型 (add, mod, del, get)
- 其他参数根据配置类型而定
**响应**: JSON格式，包含配置信息

#### 6.2 bwlist - 黑白名单管理
**路径**: `/d/bwlist`
**方法**: GET/POST
**功能**: 管理黑白名单配置
**响应**: JSON格式的黑白名单数据

### 7. IP地理位置接口

#### 7.1 locinfo - IP位置信息
**路径**: `/d/locinfo`
**方法**: GET/POST
**功能**: 查询IP地址的地理位置信息
**参数**:
- `iplist`: IP地址列表，逗号分隔
**响应**: JSON数组，包含IP位置信息

#### 7.2 geoinfo - 地理信息查询
**路径**: `/d/geoinfo`
**方法**: GET/POST
**功能**: 查询IP地址的详细地理信息（支持中英文）
**参数**:
- `iplist`: IP地址列表，逗号分隔
**响应**: JSON数组，包含中英文地理信息

### 8. 威胁情报接口

#### 8.1 threatinfo - 威胁情报查询
**路径**: `/d/threatinfo`
**方法**: GET/POST
**功能**: 查询威胁情报信息
**认证**: 需要配置文件中的KEY
**响应**: JSON格式的威胁情报数据

#### 8.2 threatinfopro - 威胁情报专业版
**路径**: `/d/threatinfopro`
**方法**: GET/POST
**功能**: 专业版威胁情报查询
**认证**: 需要API_KEY和token验证
**响应**: JSON格式的威胁情报数据

### 9. 网络信息接口

#### 9.1 ipinfo - IP信息查询
**路径**: `/d/ipinfo`
**方法**: GET/POST
**功能**: 查询IP相关信息
**参数**:
- `iplist`: IP地址列表
**响应**: JSON格式的IP信息

#### 9.2 portinfo - 端口信息查询
**路径**: `/d/portinfo`
**方法**: GET/POST
**功能**: 查询端口相关信息
**响应**: JSON格式的端口信息

### 10. 内网管理接口

#### 10.1 internalip - 内网IP管理
**路径**: `/d/internalip`
**方法**: GET/POST
**功能**: 管理内网IP地址
**参数**:
- `op`: 操作类型 (ADD, DEL, MOD, GET)
- `id`: 记录ID
- `ip`: IP地址
- `devid`: 设备ID
- `desc`: 描述信息
**响应**: JSON格式的操作结果

### 11. 证据提取接口

#### 11.1 evidence - 数据包证据提取
**路径**: `/d/evidence`
**方法**: GET/POST
**功能**: 提取网络数据包证据
**参数**:
- `devid`: 设备ID
- `time`: 时间戳（微秒）
- `time_sec`: 时间戳（秒）
- `time_usec`: 时间戳（微秒部分）
- `ip`: IP地址
- `port`: 端口号
- `download`: 是否下载 (true/false)
**响应**: JSON格式的证据数据或文件下载

### 12. TopN统计接口

#### 12.1 topn - TopN统计查询
**路径**: `/d/topn`
**方法**: GET/POST
**功能**: 获取流量TopN统计数据
**参数**:
- `devid`: 设备ID
- `starttime`: 开始时间戳
- `endtime`: 结束时间戳
- `sortby`: 排序字段
- `orderby`: 排序方式
- `limit`: 返回记录数
- `step`: 时间步长
- `ip`: IP地址
- `port`: 端口号
- `proto`: 协议
- `include`: 包含条件
- `exclude`: 排除条件
**响应**: JSON数组，包含TopN统计结果

## 通用参数说明

### 调试参数
- `dbg`: 启用调试模式，值为1时启用

### 时间参数
- 时间戳格式：Unix时间戳（秒）
- 支持starttime和endtime指定时间范围

### 设备参数
- `devid`: 设备ID，用于指定数据来源设备

## 响应格式

所有API接口统一返回JSON格式数据：

### 成功响应
```json
[
  {
    "result": "success",
    "data": {...}
  }
]
```

### 错误响应
```json
[
  {
    "result": "failed",
    "desc": "错误描述"
  }
]
```

## 认证机制

系统使用基于Session的认证机制：
1. 用户通过login接口登录获取session
2. 后续请求需要通过auth接口验证权限
3. 支持的用户角色：SYSADMIN（系统管理员）、ANALYSER（分析员）、VIEWER（查看者）

## 配置文件

### 主要配置文件
- `/etc/my.cnf.d/gl.server.cnf`: 数据库配置
- `/Server/etc/tic.conf`: 威胁情报配置
- `/Server/etc/syslogsender.conf`: 系统日志配置

### 目录结构
- `/Server/www/d/`: CGI程序部署目录
- `/Server/lib/`: 动态库目录
- `/Server/data/`: 数据文件目录
- `/Server/log/`: 日志文件目录

## 注意事项

1. 所有接口都需要通过Web服务器访问，不支持直接命令行调用（除非在开发环境）
2. 部分接口需要特定权限，请确保用户已正确认证
3. 时间参数建议使用Unix时间戳格式
4. IP地址支持IPv4和IPv6格式
5. 大部分查询接口支持分页和限制返回记录数
6. 系统支持实时数据查询和历史数据分析

## 开发说明

### 编译环境
- C++11标准
- 依赖库：cppdb, cgicc, protobuf, mysql, curl, boost
- 编译工具：g++, make

### 部署要求
- Web服务器（Apache/Nginx）支持CGI
- MySQL数据库
- 相关依赖库已安装

## API使用示例

### 1. 用户登录示例
```bash
curl -X POST "http://server/d/login" \
  -d "username=admin&password=123456"
```

### 2. 查询流量特征示例
```bash
curl "http://server/d/feature?devid=1&starttime=1640995200&endtime=1641081600&type=SUS&limit=20"
```

### 3. 获取系统状态示例
```bash
curl "http://server/d/sctl?nodetype=ALL&servicetype=BASIC&op=STATUS"
```

### 4. 查询IP地理位置示例
```bash
curl "http://server/d/locinfo?iplist=*******,*******"
```

### 5. 监控对象管理示例
```bash
# 添加监控对象
curl -X POST "http://server/d/mo" \
  -d "op=ADD&moip=*************&moport=80&protocol=TCP&desc=Web服务器"

# 查询监控对象
curl "http://server/d/mo?op=GET&devid=1"
```

## 错误代码说明

### HTTP状态码
- `200`: 请求成功
- `400`: 参数错误 (Invalid Params)
- `403`: 认证失败或权限不足
- `500`: 服务器内部错误

### 业务错误码
- `result: "failed"`: 操作失败
- `result: "success"`: 操作成功

## 数据结构说明

### 流量特征记录 (FeatureRecord)
```json
{
  "devid": 1,
  "type": "SUS",
  "time": 1640995200,
  "duration": 300,
  "protocol": 6,
  "bytes": 1024000,
  "flows": 100,
  "pkts": 2000,
  "sip": "*************",
  "sport": 80,
  "dip": "********",
  "dport": 8080,
  "peers": 50
}
```

### 事件记录 (EventRecord)
```json
{
  "devid": 1,
  "type": 2,
  "time": 1640995200,
  "protocol": 6,
  "sip": "*************",
  "sport": 80,
  "dip": "********",
  "dport": 8080,
  "domain": "example.com",
  "url": "/api/data"
}
```

### 监控对象记录 (MoRecord)
```json
{
  "id": 1,
  "moip": "*************",
  "moport": "80",
  "protocol": "TCP",
  "pip": "********",
  "pport": "8080",
  "desc": "Web服务器",
  "tag": "production",
  "devid": 1
}
```

## 性能优化建议

### 1. 查询优化
- 合理设置时间范围，避免查询过长时间段的数据
- 使用limit参数限制返回记录数
- 对于大数据量查询，建议使用分页机制

### 2. 缓存策略
- 系统支持数据缓存，重复查询相同参数会有性能提升
- 建议客户端实现适当的缓存机制

### 3. 并发控制
- 系统支持多并发请求
- 建议控制并发数量，避免对系统造成过大压力

## 安全注意事项

### 1. 认证安全
- 所有敏感操作都需要通过认证
- Session有效期限制，需要定期重新登录
- 支持多级权限控制

### 2. 参数验证
- 系统对所有输入参数进行严格验证
- 防止SQL注入和XSS攻击
- IP地址和端口号格式验证

### 3. 数据安全
- 敏感数据传输建议使用HTTPS
- 数据库连接使用加密通道
- 日志记录不包含敏感信息

## 监控和日志

### 日志文件位置
- 通用日志: `/Server/log/common`
- 命令日志: `/Server/log/cmd`
- Web访问日志: `/Server/log/www`

### 监控指标
- API响应时间
- 请求成功率
- 系统资源使用情况
- 数据库连接状态

## 故障排除

### 常见问题

#### 1. 认证失败
- 检查用户名密码是否正确
- 确认用户权限是否足够
- 检查Session是否过期

#### 2. 参数错误
- 确认必需参数是否提供
- 检查参数格式是否正确
- 验证时间戳格式

#### 3. 数据库连接问题
- 检查数据库服务状态
- 确认配置文件设置正确
- 验证数据库用户权限

#### 4. 系统服务异常
- 检查相关系统服务状态
- 查看系统日志文件
- 确认网络连接正常

### 调试方法
1. 使用`dbg=1`参数启用调试模式
2. 查看相应的日志文件
3. 使用系统控制接口检查服务状态
4. 验证数据库连接和数据完整性

## 版本更新记录

### v1.0 (2025-08-07)
- 初始版本发布
- 包含所有核心API接口
- 支持基本的流量分析和安全监控功能

---
文档版本：1.0
最后更新：2025-08-07
